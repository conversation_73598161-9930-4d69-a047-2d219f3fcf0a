package tracer

import (
	"context"

	"encoding/json"
	"github.com/Zomato/go/config"
	log "github.com/Zomato/go/logger"
	"github.com/Zomato/go/tracer"
	"os"
)

func InitialiseTracer(ctx context.Context) {
	if !IsZTracerEnabled(ctx) {
		log.Error("Datadog tracing is disabled")
		return
	}
	// Configure sampling before initializing the tracer
	configureTracingSampling(ctx)
	datadogProvider := tracer.NewDatadogProvider(ctx)
	err := tracer.Initialize(datadogProvider)
	if err != nil {
		log.FromContext(ctx).WithError(err).Error("failed to initialise tracer")
	} else {
		log.Info("Successfully initialized DataDog tracer with sampling configuration")
	}
}

// configure Tracing Sampling sets up DataDog trace sampling using environment variables
// based on the application configuration
func configureTracingSampling(ctx context.Context) {
	if !config.GetBool(ctx, "tracer.sampling.enabled") {
		return
	}
	// Get the global sampling rate
	samplingRate := config.GetFloat64(ctx, "tracer.sampling.rate")
	if samplingRate <= 0 || samplingRate > 1 {
		log.Warn(ctx, "Invalid sampling rate, using default", "configured_rate", samplingRate)
		samplingRate = 1.0 // Default to 100% if invalid
	}
	// Get application name for service
	serviceName := config.GetString(ctx, "statsd.application")
	if serviceName == "" {
		serviceName = "flash_gateway"
	}
	// Create sampling rules from config
	samplingRules := []map[string]interface{}{}
	// Add rules from config if available
	if rules := config.Get(ctx, "tracer.sampling.rules"); rules != nil {
		if rulesArr, ok := rules.([]interface{}); ok {
			for _, r := range rulesArr {
				if rule, ok := r.(map[string]interface{}); ok {
					// Get service from rule or default
					ruleService, _ := rule["service"].(string)
					if ruleService == "" {
						ruleService = serviceName
					}
					// Get operation regex if available
					opRegex, _ := rule["operation_name_regex"].(string)
					// Get sample rate
					ruleRate, ok := rule["sample_rate"].(float64)
					if !ok || ruleRate <= 0 || ruleRate > 1 {
						continue
					}
					ruleMap := map[string]interface{}{
						"service":     ruleService,
						"sample_rate": ruleRate,
					}
					// Add resource (operation regex) if provided
					if opRegex != "" {
						ruleMap["name"] = opRegex
					}
					samplingRules = append(samplingRules, ruleMap)
				}
			}
		}
	}
	// Add default rule for overall sampling rate
	samplingRules = append(samplingRules, map[string]interface{}{
		"service":     serviceName,
		"sample_rate": samplingRate,
	})
	// Convert rules to JSON and set environment variable
	if len(samplingRules) > 0 {
		rulesJSON, err := json.Marshal(samplingRules)
		if err == nil {
			os.Setenv("DD_TRACE_SAMPLING_RULES", string(rulesJSON))
			log.Info(ctx, "Set DataDog trace sampling rules", "rules", string(rulesJSON))
			// Print the rules in a clearly visible format
			log.Info(ctx, "========================================")
			log.Info(ctx, "DATADOG SAMPLING RULES JSON")
			log.Info(ctx, string(rulesJSON))
			log.Info(ctx, "========================================")
		} else {
			log.Error(ctx, "Failed to set sampling rules", "error", err)
		}
	}
	// Configure additional sampling options
	os.Setenv("DD_TRACE_SAMPLE_RATE", "0") // Disable legacy sample rate to use rules
}
func IsZTracerEnabled(ctx context.Context) bool {
	return config.GetBool(ctx, "tracer.enabled")
}
