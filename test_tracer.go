package main

import (
	"encoding/json"
	"fmt"
)

// Standalone version that simulates the JSON building logic from tracer.go
func buildSamplingRulesJSON(samplingEnabled bool, samplingRate float64, serviceName string, configRules []interface{}) string {
	if !samplingEnabled {
		return "Sampling disabled"
	}

	// Validate sampling rate
	if samplingRate <= 0 || samplingRate > 1 {
		fmt.Printf("Invalid sampling rate %f, using default 1.0\n", samplingRate)
		samplingRate = 1.0 // Default to 100% if invalid
	}

	// Default service name
	if serviceName == "" {
		serviceName = "flash_gateway"
	}

	// Create sampling rules
	samplingRules := []map[string]interface{}{}

	// Add rules from config if available
	if configRules != nil {
		for _, r := range configRules {
			if rule, ok := r.(map[string]interface{}); ok {
				// Get service from rule or default
				ruleService, _ := rule["service"].(string)
				if ruleService == "" {
					ruleService = serviceName
				}
				// Get operation regex if available
				opRegex, _ := rule["operation_name_regex"].(string)
				// Get sample rate
				ruleRate, ok := rule["sample_rate"].(float64)
				if !ok || ruleRate <= 0 || ruleRate > 1 {
					continue
				}
				ruleMap := map[string]interface{}{
					"service":     ruleService,
					"sample_rate": ruleRate,
				}
				// Add resource (operation regex) if provided
				if opRegex != "" {
					ruleMap["name"] = opRegex
				}
				samplingRules = append(samplingRules, ruleMap)
			}
		}
	}

	// Add default rule for overall sampling rate
	samplingRules = append(samplingRules, map[string]interface{}{
		"service":     serviceName,
		"sample_rate": samplingRate,
	})

	// Convert rules to JSON and return
	if len(samplingRules) > 0 {
		rulesJSON, err := json.Marshal(samplingRules)
		if err == nil {
			return string(rulesJSON)
		} else {
			return fmt.Sprintf("Error marshaling JSON: %v", err)
		}
	}

	return "No rules generated"
}

func main() {
	fmt.Println("Testing tracer sampling configuration...")
	fmt.Println("========================================")

	// Test scenario 1: Basic configuration
	fmt.Println("\n--- Test 1: Basic configuration ---")
	rulesJSON := buildSamplingRulesJSON(true, 0.5, "flash_gateway", nil)
	fmt.Println("Final rulesJSON:")
	fmt.Println(rulesJSON)
	prettyPrintJSON(rulesJSON)

	// Test scenario 2: With custom rules
	fmt.Println("\n--- Test 2: With custom rules ---")
	customRules := []interface{}{
		map[string]interface{}{
			"service":              "custom_service",
			"operation_name_regex": "GET /api/*",
			"sample_rate":          0.1,
		},
		map[string]interface{}{
			"sample_rate": 0.8, // No service specified, should use default
		},
	}
	rulesJSON2 := buildSamplingRulesJSON(true, 0.3, "", customRules)
	fmt.Println("Final rulesJSON:")
	fmt.Println(rulesJSON2)
	prettyPrintJSON(rulesJSON2)

	// Test scenario 3: Disabled sampling
	fmt.Println("\n--- Test 3: Disabled sampling ---")
	rulesJSON3 := buildSamplingRulesJSON(false, 0.5, "flash_gateway", nil)
	fmt.Println("Final rulesJSON:")
	fmt.Println(rulesJSON3)

	// Test scenario 4: Invalid sampling rate
	fmt.Println("\n--- Test 4: Invalid sampling rate ---")
	rulesJSON4 := buildSamplingRulesJSON(true, 1.5, "flash_gateway", nil)
	fmt.Println("Final rulesJSON:")
	fmt.Println(rulesJSON4)
	prettyPrintJSON(rulesJSON4)
}

func prettyPrintJSON(rulesJSON string) {
	if rulesJSON != "Sampling disabled" && rulesJSON != "No rules generated" && rulesJSON[0:5] != "Error" {
		var rules []map[string]interface{}
		if err := json.Unmarshal([]byte(rulesJSON), &rules); err == nil {
			prettyJSON, _ := json.MarshalIndent(rules, "", "  ")
			fmt.Println("Pretty printed JSON:")
			fmt.Println(string(prettyJSON))
		}
	}
}
